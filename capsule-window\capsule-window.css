/* 胶囊翻译小窗样式 */

/* CSS变量定义 - 与主程序保持一致 */
:root {
    --capsule-radius: 12px;

    /* 颜色变量 - 亮色主题（与主程序完全一致） */
    --bg-primary: #f4f4f4;
    --bg-secondary: #f4f4f4;
    --bg-tertiary: #eeeeee;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-tertiary: #999999;
    --border-primary: #e0e0e0;
    --border-secondary: #cccccc;

    /* 输入输出区域专用背景色 */
    --area-input: #f9f9f9;
    --area-output: #f9f9f9;
    --area-buttons: #f9f9f9;
    --accent-primary: #f4f4f4;
    --accent-secondary: #333333;
    --accent-hover: #eeeeee;
    --primary-btn-bg: #e8e8e8;
    --primary-btn-text: #333333;
    --primary-btn-hover: #dddddd;
    --shadow-light: rgba(0, 0, 0, 0.06);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-heavy: rgba(0, 0, 0, 0.15);

    /* 强调色 */
    --accent-color: #1976d2;
    --primary-color: #1976d2;
    --primary-color-alpha: rgba(25, 118, 210, 0.1);

    /* 字体变量 */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    --font-size-sm: 11px;
    --font-size-normal: 12px;
    --font-size-base: 14px;
    --font-size-md: 15px;
    --font-size-lg: 16px;

    /* 行高变量 */
    --line-height-tight: 1.2;
    --line-height-normal: 1.4;
    --line-height-relaxed: 1.6;

    /* 过渡动画变量 */
    --transition-fast: all 0.2s ease;
    --border-radius-sm: 8px;
}

/* 暗色主题 - 与主程序保持一致 */
[data-theme="dark"] {
    --bg-primary: #303133;
    --bg-secondary: #303133;
    --bg-tertiary: #3a3c3f;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-tertiary: #999999;
    --border-primary: #4a4c4f;
    --border-secondary: #5a5c5f;

    /* 输入输出区域专用背景色 */
    --area-input: #282a2d;
    --area-output: #282a2d;
    --area-buttons: #282a2d;
    --accent-primary: #303133;
    --accent-secondary: #ffffff;
    --accent-hover: #3a3c3f;
    --primary-btn-bg: #4a4c4f;
    --primary-btn-text: #ffffff;
    --primary-btn-hover: #5a5c5f;
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.4);
    --shadow-heavy: rgba(0, 0, 0, 0.5);

    /* 强调色 */
    --accent-color: #42a5f5;
    --primary-color: #42a5f5;
    --primary-color-alpha: rgba(66, 165, 245, 0.15);
}



/* 容器圆角设置 */
.capsule-container {
    border-radius: var(--capsule-radius);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    background: transparent;
    overflow: hidden;
    width: 100%;
    height: 100%;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    background: transparent;
    color: var(--text-primary);
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    /* 改善字体渲染质量 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.capsule-container {
    width: 100%;
    height: 100vh;
    background: rgba(244, 244, 244, 0.96);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    box-shadow: 0 8px 32px var(--shadow-medium),
                0 2px 8px var(--shadow-light);
    border: 1px solid var(--border-primary);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    margin: 0;
    padding: 0;
    /* 圆角裁剪 */
    -webkit-clip-path: inset(0 round var(--capsule-radius));
    clip-path: inset(0 round var(--capsule-radius));
}

/* 暗色主题适配 */
[data-theme="dark"] .capsule-container {
    background: rgba(48, 49, 51, 0.96);
    border: 1px solid var(--border-primary);
}

/* 兼容系统主题偏好 - 使用与主程序一致的边框颜色 */
@media (prefers-color-scheme: dark) {
    .capsule-container:not([data-theme]) {
        background: rgba(48, 49, 51, 0.96);
        border: 1px solid #4a4c4f !important;
    }
}

/* 确保主题边框颜色优先级最高 */
.capsule-container[data-theme="light"] {
    border: 1px solid #e0e0e0 !important;
}

.capsule-container[data-theme="dark"] {
    border: 1px solid #4a4c4f !important;
}

/* 拖拽区域 */
.drag-area {
    height: 40px;
    background: transparent;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 12px;
    -webkit-app-region: drag;
    cursor: move;
}

/* 翻译控制区域 - 复刻主程序样式 */
.translate-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--area-buttons);
    border-radius: 8px;
    border: 1px solid var(--border-primary);
    gap: 3px;
    min-height: 48px;
    margin: 0;
}

.translate-controls .left-controls,
.translate-controls .center-controls,
.translate-controls .right-controls {
    display: flex;
    align-items: center;
    gap: 6.5px;
}

/* 翻译模型选择器样式 */
.translate-model-selector {
    display: flex;
    align-items: center;
    gap: 4px;
    max-width: 160px;
}

.translate-model-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    width: 28px;
    height: 28px;
    box-sizing: border-box;
}

.translate-model-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.translate-model-btn.active {
    background: rgba(25, 118, 210, 0.08);
    color: #1976d2;
    font-weight: 500;
    border-color: rgba(25, 118, 210, 0.3);
}

.model-icon {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
}

/* 状态指示器 */
.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.status-text {
    font-size: 11px;
    color: var(--text-tertiary);
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
}

/* 语言选择器容器 */
.language-selector-container {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0;
    background: transparent;
    border: none;
    border-radius: 6px;
}

/* 语言选择器 */
.language-selector {
    position: relative;
}

.language-select {
    appearance: none;
    background: #e6f3ff;
    color: #1e40af;
    border: 1px solid #60a5fa;
    border-radius: 8px;
    padding: 6px 8px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    width: 44px;
    height: 32px;
    box-sizing: border-box;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.language-select .language-text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 11px;
}

.language-select:hover {
    background: #dbeafe;
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
}

.language-select:focus {
    outline: none;
    background: #dbeafe;
    border-color: #3b82f6;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
}

/* 语言交换按钮 */
.language-swap-btn {
    width: 24px;
    height: 24px;
    border: 1px solid var(--border-primary);
    background: var(--bg-secondary);
    border-radius: 4px;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.language-swap-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
}

/* 语言选择器下拉菜单 */
.language-select-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow-medium);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    display: none;
    margin-top: 4px;
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏Webkit浏览器的滚动条 */
.language-select-menu::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

/* 确保在所有主题下都隐藏滚动条 */
.language-select-menu::-webkit-scrollbar-track {
    display: none;
}

.language-select-menu::-webkit-scrollbar-thumb {
    display: none;
}

.language-select-menu.show {
    display: block;
}

.language-option {
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--text-primary);
    transition: background-color 0.2s ease;
}

.language-option:hover {
    background: var(--bg-secondary);
}

.language-option.selected {
    background: rgba(25, 118, 210, 0.08);
    color: #1976d2;
    font-weight: 500;
}

/* 暗色主题适配 - 翻译控制区域 */
[data-theme="dark"] .translate-model-btn {
    background: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .translate-model-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
}

[data-theme="dark"] .translate-model-btn.active {
    background: rgba(25, 118, 210, 0.08);
    color: #1976d2;
    border-color: rgba(25, 118, 210, 0.3);
}

[data-theme="dark"] .language-select {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    border-color: rgba(59, 130, 246, 0.5);
}

[data-theme="dark"] .language-select:hover {
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.7);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

[data-theme="dark"] .language-select:focus {
    background: rgba(59, 130, 246, 0.3);
    border-color: rgba(59, 130, 246, 0.7);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
}

[data-theme="dark"] .language-swap-btn {
    background: var(--bg-secondary);
    border-color: var(--border-primary);
    color: var(--text-secondary);
}

[data-theme="dark"] .language-swap-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border-color: var(--border-secondary);
}

[data-theme="dark"] .language-option.selected {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
}

/* 暗色主题下的语言选择器下拉菜单滚动条隐藏 */
[data-theme="dark"] .language-select-menu {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

[data-theme="dark"] .language-select-menu::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

[data-theme="dark"] .status-text {
    color: var(--text-tertiary);
}

.window-controls {
    -webkit-app-region: no-drag;
}

.close-btn {
    width: 24px;
    height: 24px;
    border: 1px solid var(--border-primary);
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
}

.close-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
    box-shadow: 0 4px 12px var(--shadow-medium);
    transform: scale(1.05);
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 0 16px 16px 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 输入区域 */
.input-section {
    flex: 1;
}

.input-container {
    position: relative;
    height: 100%;
}

.input-text {
    width: 100%;
    height: 100%;
    border: 1px solid var(--border-primary);
    background: var(--area-input);
    border-radius: var(--capsule-radius);
    padding: 12px 50px 12px 12px;
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    resize: none;
    outline: none;
    font-family: var(--font-family);
    transition: var(--transition-fast);
}

.input-text:focus {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color-alpha);
}

.input-text::placeholder {
    color: var(--text-tertiary);
}

/* 暗色主题适配 */
[data-theme="dark"] .input-text {
    background: var(--area-input);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .input-text:focus {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

[data-theme="dark"] .input-text::placeholder {
    color: var(--text-tertiary);
}

/* 兼容系统主题偏好 */
@media (prefers-color-scheme: dark) {
    .input-text:not([data-theme]) {
        background: rgba(255, 255, 255, 0.05);
        color: #ffffff;
    }

    .input-text:not([data-theme]):focus {
        background: rgba(255, 255, 255, 0.08);
    }

    .input-text:not([data-theme])::placeholder {
        color: rgba(255, 255, 255, 0.5);
    }
}

.input-actions {
    position: absolute;
    right: 8px;
    bottom: 8px;
    display: flex;
    gap: 4px;
    z-index: 10;
}

/* 分隔线 */
.divider {
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-primary), transparent);
    margin: 0 -20px;
}

/* 暗色主题适配 */
[data-theme="dark"] .divider {
    background: linear-gradient(90deg, transparent, var(--border-primary), transparent);
}

/* 兼容系统主题偏好 */
@media (prefers-color-scheme: dark) {
    .divider:not([data-theme]) {
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    }
}

/* 输出区域 */
.output-section {
    flex: 1;
}

.output-container {
    position: relative;
    height: 100%;
}

.output-text {
    width: 100%;
    height: 100%;
    background: var(--area-output);
    border: 1px solid var(--border-primary);
    border-radius: var(--capsule-radius);
    padding: 12px 50px 12px 12px;
    font-size: var(--font-size-base);
    line-height: var(--line-height-relaxed);
    color: var(--text-primary);
    overflow-y: auto;
    user-select: text;
    -webkit-user-select: text;
}

.placeholder-text {
    color: var(--text-tertiary);
    font-style: italic;
}

/* 暗色主题适配 */
[data-theme="dark"] .output-text {
    background: var(--area-output);
    border-color: var(--border-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .placeholder-text {
    color: var(--text-tertiary);
}

/* 兼容系统主题偏好 */
@media (prefers-color-scheme: dark) {
    .output-text:not([data-theme]) {
        background: rgba(255, 255, 255, 0.03);
        color: #ffffff;
    }

    .placeholder-text:not([data-theme]) {
        color: rgba(255, 255, 255, 0.4);
    }
}

.output-actions {
    position: absolute;
    right: 8px;
    top: 8px;
}

/* 按钮样式 */
.action-btn {
    width: 28px;
    height: 28px;
    border: 1px solid var(--border-primary);
    background: var(--bg-secondary);
    border-radius: 6px;
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.action-btn .btn-icon {
    width: 14px;
    height: 14px;
}

.action-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
    box-shadow: 0 4px 12px var(--shadow-medium);
    transform: translateY(-1px);
}

.translate-btn {
    background: rgba(25, 118, 210, 0.08);
    color: #1976d2;
    border-color: var(--border-primary);
}

.translate-btn:hover {
    background: #1976d2;
    color: white;
    border-color: var(--border-primary);
}

/* 暗色主题适配 */
[data-theme="dark"] .action-btn {
    background: var(--area-input);
    border-color: var(--border-primary);
    color: var(--text-secondary);
}

[data-theme="dark"] .action-btn:hover {
    background: var(--bg-secondary);
    border-color: var(--border-secondary);
}

[data-theme="dark"] .translate-btn {
    background: var(--primary-color-alpha);
    color: var(--primary-color);
    border-color: var(--border-secondary);
}

[data-theme="dark"] .translate-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--border-secondary);
}

/* 兼容系统主题偏好 */
@media (prefers-color-scheme: dark) {
    .action-btn:not([data-theme]) {
        background: rgba(255, 255, 255, 0.08);
        color: rgba(255, 255, 255, 0.6);
    }

    .action-btn:not([data-theme]):hover {
        background: rgba(255, 255, 255, 0.12);
        color: rgba(255, 255, 255, 0.8);
    }
}



/* 加载指示器 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(244, 244, 244, 0.8);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--capsule-radius);
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--primary-color-alpha);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 8px;
}

.loading-text {
    font-size: var(--font-size-normal);
    color: var(--text-secondary);
}

/* 暗色主题适配 */
[data-theme="dark"] .loading-overlay {
    background: rgba(48, 49, 51, 0.8);
}

[data-theme="dark"] .loading-text {
    color: var(--text-secondary);
}

/* 兼容系统主题偏好 */
@media (prefers-color-scheme: dark) {
    .loading-overlay:not([data-theme]) {
        background: rgba(30, 30, 30, 0.8);
    }

    .loading-text:not([data-theme]) {
        color: rgba(255, 255, 255, 0.6);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 滚动条样式 */
.output-text::-webkit-scrollbar {
    width: 4px;
}

.output-text::-webkit-scrollbar-track {
    background: transparent;
}

.output-text::-webkit-scrollbar-thumb {
    background: var(--text-tertiary);
    border-radius: 2px;
}

.output-text::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 暗色主题适配 */
[data-theme="dark"] .output-text::-webkit-scrollbar-thumb {
    background: var(--text-tertiary);
}

[data-theme="dark"] .output-text::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 兼容系统主题偏好 */
@media (prefers-color-scheme: dark) {
    .output-text:not([data-theme])::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.2);
    }

    /* 系统暗色主题下的语言选择器下拉菜单滚动条隐藏 */
    .language-select-menu:not([data-theme]) {
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
    }

    .language-select-menu:not([data-theme])::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }
}

/* 弹窗通知样式 */
.notification-container {
    position: fixed;
    top: 16px;
    right: 16px;
    z-index: 9999;
    pointer-events: none;
}

.notification {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 8px;
    box-shadow: 0 4px 12px var(--shadow-medium);
    color: var(--text-primary);
    font-size: 12px;
    font-weight: 500;
    max-width: 200px;
    word-wrap: break-word;
    pointer-events: auto;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-color: #4caf50;
    background: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
}

.notification.error {
    border-color: #f44336;
    background: rgba(244, 67, 54, 0.1);
    color: #c62828;
}

.notification.warning {
    border-color: #ff9800;
    background: rgba(255, 152, 0, 0.1);
    color: #ef6c00;
}

.notification.info {
    border-color: #2196f3;
    background: rgba(33, 150, 243, 0.1);
    color: #1565c0;
}

/* 暗色主题适配 - 弹窗通知 */
[data-theme="dark"] .notification {
    background: var(--bg-tertiary);
    border-color: var(--border-secondary);
    color: var(--text-primary);
}

[data-theme="dark"] .notification.success {
    border-color: #66bb6a;
    background: rgba(102, 187, 106, 0.2);
    color: #81c784;
}

[data-theme="dark"] .notification.error {
    border-color: #ef5350;
    background: rgba(239, 83, 80, 0.2);
    color: #e57373;
}

[data-theme="dark"] .notification.warning {
    border-color: #ffb74d;
    background: rgba(255, 183, 77, 0.2);
    color: #ffcc02;
}

[data-theme="dark"] .notification.info {
    border-color: #42a5f5;
    background: rgba(66, 165, 245, 0.2);
    color: #64b5f6;
}

// 胶囊翻译小窗 JavaScript 逻辑

class CapsuleTranslator {
    constructor() {
        this.isTranslating = false;
        this.currentWindow = null;
        this.init();
    }

    init() {
        // 首先初始化主题，确保边框颜色正确
        this.initTheme();

        this.bindEvents();
        this.setupWindow();

        setTimeout(() => {
            this.ensureRoundedCorners();
            this.applyOptimalTransparency();
        }, 200);
        this.updateStatus('就绪');

        this.startRoundedCornersWatcher();
    }

    // 初始化主题，确保与主程序保持一致
    initTheme() {
        try {
            // 尝试从 uTools 获取主题信息
            let theme = 'light'; // 默认亮色主题

            if (typeof utools !== 'undefined' && typeof utools.isDarkColors === 'function') {
                try {
                    theme = utools.isDarkColors() ? 'dark' : 'light';
                } catch (error) {
                    console.warn('无法获取 uTools 主题信息，使用默认主题');
                }
            } else {
                // 如果不在 uTools 环境中，检查系统主题偏好
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    theme = 'dark';
                }
            }

            // 设置主题属性，确保CSS变量正确应用
            document.documentElement.setAttribute('data-theme', theme);

            console.log(`小窗主题已初始化为: ${theme}`);
        } catch (error) {
            console.error('主题初始化失败:', error);
            // 确保至少设置一个默认主题
            document.documentElement.setAttribute('data-theme', 'light');
        }
    }

    bindEvents() {
        // 关闭按钮
        document.getElementById('close-btn').addEventListener('click', () => {
            this.closeWindow();
        });

        // 清空按钮
        document.getElementById('clear-btn').addEventListener('click', () => {
            this.clearInput();
        });

        // 翻译按钮
        document.getElementById('translate-btn').addEventListener('click', () => {
            this.translate();
        });

        // 复制按钮
        document.getElementById('copy-btn').addEventListener('click', () => {
            this.copyResult();
        });

        // 输入框事件
        const inputText = document.getElementById('input-text');
        inputText.addEventListener('input', () => {
            this.onInputChange();
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        // 语言选择器按钮事件
        document.getElementById('source-lang').addEventListener('click', () => {
            this.toggleLanguageMenu('source');
        });

        document.getElementById('target-lang').addEventListener('click', () => {
            this.toggleLanguageMenu('target');
        });

        // 语言交换按钮
        document.getElementById('swap-languages').addEventListener('click', () => {
            this.swapLanguages();
        });

        // 绑定模型切换按钮
        this.bindModelButtons();

        // 语言选项点击事件
        this.bindLanguageOptionEvents();

        // 监听主题变化
        if (window.matchMedia) {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeQuery.addEventListener('change', () => {
                setTimeout(() => {
                    // 重新初始化主题，确保边框颜色正确
                    this.initTheme();
                    this.applyOptimalTransparency();
                }, 100);
            });
        }
    }

    setupWindow() {
        // 获取当前窗口引用（如果在 uTools 环境中）
        if (typeof utools !== 'undefined') {
            this.currentWindow = utools.getCurrentWindow?.();
        }

        // 设置窗口初始状态
        this.ensureRoundedCorners();
    }

    // 启动圆角监视器 - 最小化版本
    startRoundedCornersWatcher() {

        this.roundedCornersInterval = setInterval(() => {
            this.ensureRoundedCorners();
        }, 30000);


        window.addEventListener('resize', () => {
            setTimeout(() => {
                this.ensureRoundedCorners();
            }, 300);
        });
    }

    closeWindow() {
        try {
            // 清理监视器
            if (this.roundedCornersInterval) {
                clearInterval(this.roundedCornersInterval);
            }

            if (typeof utools !== 'undefined' && this.currentWindow) {
                this.currentWindow.close();
            } else if (window.close) {
                window.close();
            }
        } catch (error) {
            console.error('关闭窗口失败:', error);
        }
    }

    clearInput() {
        const inputText = document.getElementById('input-text');
        const outputText = document.getElementById('output-text');
        
        inputText.value = '';
        outputText.innerHTML = '<div class="placeholder-text">翻译结果将显示在这里...</div>';
        
        this.hideCopyButton();
        this.updateStatus('已清空');
        
        // 聚焦到输入框
        inputText.focus();
    }

    async translate() {
        const inputText = document.getElementById('input-text');
        const text = inputText.value.trim();

        if (!text) {
            this.updateStatus('请输入要翻译的文本');
            return;
        }

        if (this.isTranslating) {
            return;
        }

        this.isTranslating = true;
        this.showLoading();
        this.updateStatus('翻译中...');

        try {
            // TODO: 集成实际翻译服务
            this.showError('翻译功能开发中，敬请期待');
        } catch (error) {
            this.showError('翻译失败，请稍后重试');
        } finally {
            this.isTranslating = false;
            this.hideLoading();
        }
    }



    copyResult() {
        const outputText = document.getElementById('output-text');
        const text = outputText.textContent;

        if (!text || text.includes('翻译结果将显示在这里')) {
            return;
        }

        try {
            // 尝试使用 uTools API 复制
            if (typeof utools !== 'undefined' && utools.copyText) {
                utools.copyText(text);
                this.updateStatus('已复制到剪贴板');
            } else {
                // 备用复制方法
                navigator.clipboard.writeText(text).then(() => {
                    this.updateStatus('已复制到剪贴板');
                }).catch(() => {
                    this.updateStatus('复制失败');
                });
            }
        } catch (error) {
            console.error('复制失败:', error);
            this.updateStatus('复制失败');
        }
    }

    onInputChange() {
        const inputText = document.getElementById('input-text');
        const hasText = inputText.value.trim().length > 0;
        
        if (hasText) {
            this.updateStatus('准备翻译');
        } else {
            this.updateStatus('就绪');
        }
    }

    handleKeydown(e) {
        // Ctrl/Cmd + Enter 翻译
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            this.translate();
        }
        
        // Escape 关闭窗口
        if (e.key === 'Escape') {
            e.preventDefault();
            this.closeWindow();
        }
        
        // Ctrl/Cmd + L 清空
        if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
            e.preventDefault();
            this.clearInput();
        }
    }

    // 绑定语言选项点击事件
    bindLanguageOptionEvents() {
        // 源语言选项
        const sourceOptions = document.querySelectorAll('#source-lang-menu .language-option');
        sourceOptions.forEach(option => {
            option.addEventListener('click', () => {
                this.selectLanguage('source', option.dataset.value, option);
            });
        });

        // 目标语言选项
        const targetOptions = document.querySelectorAll('#target-lang-menu .language-option');
        targetOptions.forEach(option => {
            option.addEventListener('click', () => {
                this.selectLanguage('target', option.dataset.value, option);
            });
        });

        // 点击其他地方关闭菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.language-selector')) {
                this.closeAllLanguageMenus();
            }
        });
    }

    // 切换语言菜单显示状态
    toggleLanguageMenu(type) {
        const menu = document.getElementById(`${type}-lang-menu`);
        const otherMenu = document.getElementById(`${type === 'source' ? 'target' : 'source'}-lang-menu`);

        if (!menu) return;

        // 关闭其他菜单
        if (otherMenu) {
            otherMenu.classList.remove('show');
        }

        // 切换当前菜单
        menu.classList.toggle('show');
    }

    // 关闭所有语言菜单
    closeAllLanguageMenus() {
        const sourceMenu = document.getElementById('source-lang-menu');
        const targetMenu = document.getElementById('target-lang-menu');

        if (sourceMenu) sourceMenu.classList.remove('show');
        if (targetMenu) targetMenu.classList.remove('show');
    }

    // 选择语言
    selectLanguage(type, _langCode, optionElement) {
        const button = document.getElementById(`${type}-lang`);
        const menu = document.getElementById(`${type}-lang-menu`);

        if (!button || !menu) return;

        // 更新按钮显示
        const languageText = button.querySelector('.language-text');
        if (languageText && optionElement) {
            const label = optionElement.querySelector('span').textContent;
            languageText.textContent = label;
        }

        // 更新选中状态
        menu.querySelectorAll('.language-option').forEach(opt => {
            opt.classList.remove('selected');
        });
        optionElement.classList.add('selected');

        // 关闭菜单
        menu.classList.remove('show');

        // 更新状态
        this.updateStatus(`已选择${type === 'source' ? '源' : '目标'}语言: ${optionElement.querySelector('span').textContent}`);
    }

    // 交换语言
    swapLanguages() {
        const sourceBtn = document.getElementById('source-lang');
        const targetBtn = document.getElementById('target-lang');
        const sourceMenu = document.getElementById('source-lang-menu');
        const targetMenu = document.getElementById('target-lang-menu');

        if (!sourceBtn || !targetBtn || !sourceMenu || !targetMenu) return;

        // 获取当前选中的语言
        const sourceSelected = sourceMenu.querySelector('.language-option.selected');
        const targetSelected = targetMenu.querySelector('.language-option.selected');

        if (!sourceSelected || !targetSelected) return;

        // 如果源语言是自动检测，不允许交换
        if (sourceSelected.dataset.value === 'auto') {
            this.updateStatus('自动检测语言无法交换');
            return;
        }

        // 交换按钮显示
        const sourceText = sourceBtn.querySelector('.language-text').textContent;
        const targetText = targetBtn.querySelector('.language-text').textContent;

        sourceBtn.querySelector('.language-text').textContent = targetText;
        targetBtn.querySelector('.language-text').textContent = sourceText;

        // 交换选中状态
        const sourceValue = sourceSelected.dataset.value;
        const targetValue = targetSelected.dataset.value;

        // 更新源语言菜单选中状态
        sourceMenu.querySelectorAll('.language-option').forEach(opt => {
            opt.classList.remove('selected');
            if (opt.dataset.value === targetValue) {
                opt.classList.add('selected');
            }
        });

        // 更新目标语言菜单选中状态
        targetMenu.querySelectorAll('.language-option').forEach(opt => {
            opt.classList.remove('selected');
            if (opt.dataset.value === sourceValue) {
                opt.classList.add('selected');
            }
        });

        this.updateStatus('语言已交换');
    }

    onLanguageChange() {
        // 保留原有方法以兼容性
        this.updateStatus('语言设置已更改');
    }

    // 应用最佳透明效果
    applyOptimalTransparency() {
        const container = document.querySelector('.capsule-container');
        if (container) {
            // 根据当前主题应用透明效果
            const currentTheme = document.documentElement.getAttribute('data-theme');

            if (currentTheme === 'dark') {
                container.style.background = 'rgba(48, 49, 51, 0.96)';
            } else {
                container.style.background = 'rgba(244, 244, 244, 0.96)';
            }

            container.style.backdropFilter = 'blur(8px)';
            container.style.webkitBackdropFilter = 'blur(8px)';
        }
    }



    // 确保圆角效果始终存在 - 轻量级检查
    ensureRoundedCorners() {
        const container = document.querySelector('.capsule-container');

        if (container) {
            // 只检查和修复关键的圆角样式，避免过度干预
            const currentRadius = getComputedStyle(container).borderRadius;
            if (!currentRadius || currentRadius === '0px' || currentRadius === 'none') {
                container.style.borderRadius = '12px';
                container.style.clipPath = 'inset(0 round 12px)';
                container.style.webkitClipPath = 'inset(0 round 12px)';
                container.style.overflow = 'hidden';
            }
        }
    }

    showLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        loadingOverlay.style.display = 'flex';
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        loadingOverlay.style.display = 'none';
    }

    showCopyButton() {
        const copyBtn = document.getElementById('copy-btn');
        copyBtn.style.display = 'flex';
    }

    hideCopyButton() {
        const copyBtn = document.getElementById('copy-btn');
        copyBtn.style.display = 'none';
    }

    showError(message) {
        const outputText = document.getElementById('output-text');
        outputText.innerHTML = `<div style="color: #ff3b30;">${message}</div>`;
        this.updateStatus('错误');
    }

    // 显示弹窗通知
    showNotification(message, type = 'info') {
        const container = document.getElementById('notification-container');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        container.appendChild(notification);

        // 触发显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // 自动移除
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // 更新状态显示（改为弹窗形式）
    updateStatus(message) {
        // 将状态更新改为弹窗通知
        if (message && message !== '就绪') {
            this.showNotification(message, 'info');
        }
    }

    // 绑定模型按钮事件
    bindModelButtons() {
        const modelButtons = document.querySelectorAll('.translate-model-btn');
        modelButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.switchModel(button.dataset.service);
            });
        });
    }

    // 切换翻译模型
    switchModel(service) {
        // 更新按钮状态
        document.querySelectorAll('.translate-model-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        const selectedBtn = document.querySelector(`[data-service="${service}"]`);
        if (selectedBtn) {
            selectedBtn.classList.add('active');
            const serviceName = selectedBtn.title;
            this.showNotification(`已切换到 ${serviceName}`, 'success');
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.capsuleTranslator = new CapsuleTranslator();
});

// 如果在 uTools 环境中，监听父窗口消息
if (typeof utools !== 'undefined') {
    // 可以在这里添加与主窗口的通信逻辑
}

// 胶囊翻译小窗 JavaScript 逻辑

class CapsuleTranslator {
    constructor() {
        this.isTranslating = false;
        this.currentWindow = null;
        this.init();
    }

    init() {
        // 首先初始化主题，确保边框颜色正确
        this.initTheme();

        this.bindEvents();
        this.setupWindow();

        // 同步主应用配置
        this.syncConfigFromMainApp();

        // 初始化翻译模型选择器
        this.initTranslateModelSelector();

        setTimeout(() => {
            this.ensureRoundedCorners();
            this.applyOptimalTransparency();
        }, 200);
        this.updateStatus('就绪');

        this.startRoundedCornersWatcher();

        // 处理传入的文本（如果有）
        this.handleIncomingText();
    }

    // 处理传入的文本
    handleIncomingText() {
        try {
            let textToTranslate = null;

            // 尝试从uTools获取payload
            if (typeof utools !== 'undefined' && utools.getPayload) {
                try {
                    const payload = utools.getPayload();
                    if (payload) {
                        if (typeof payload === 'string') {
                            textToTranslate = payload;
                        } else if (payload.text) {
                            textToTranslate = payload.text;
                        } else if (payload.data) {
                            textToTranslate = payload.data;
                        }
                    }
                } catch (error) {
                    console.warn('获取uTools payload失败:', error);
                }
            }

            // 尝试从全局变量获取
            if (!textToTranslate && window.pendingTranslateText) {
                textToTranslate = window.pendingTranslateText;
                window.pendingTranslateText = null; // 清除全局变量
            }

            // 尝试从URL参数获取
            if (!textToTranslate) {
                const urlParams = new URLSearchParams(window.location.search);
                textToTranslate = urlParams.get('text');
            }

            // 如果有文本，填入输入框
            if (textToTranslate && textToTranslate.trim()) {
                const inputText = document.getElementById('input-text');
                if (inputText) {
                    inputText.value = textToTranslate.trim();
                    this.onInputChange();

                    // 可选：自动执行翻译
                    // setTimeout(() => this.translate(), 500);
                }
            }
        } catch (error) {
            console.error('处理传入文本失败:', error);
        }
    }

    // 初始化主题，确保与主程序保持一致
    initTheme() {
        try {
            // 尝试从 uTools 获取主题信息
            let theme = 'light'; // 默认亮色主题

            if (typeof utools !== 'undefined' && typeof utools.isDarkColors === 'function') {
                try {
                    theme = utools.isDarkColors() ? 'dark' : 'light';
                } catch (error) {
                    console.warn('无法获取 uTools 主题信息，使用默认主题');
                }
            } else {
                // 如果不在 uTools 环境中，检查系统主题偏好
                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                    theme = 'dark';
                }
            }

            // 设置主题属性，确保CSS变量正确应用
            document.documentElement.setAttribute('data-theme', theme);

            console.log(`小窗主题已初始化为: ${theme}`);
        } catch (error) {
            console.error('主题初始化失败:', error);
            // 确保至少设置一个默认主题
            document.documentElement.setAttribute('data-theme', 'light');
        }
    }

    bindEvents() {
        // 关闭按钮
        document.getElementById('close-btn').addEventListener('click', () => {
            this.closeWindow();
        });

        // 清空按钮
        document.getElementById('clear-btn').addEventListener('click', () => {
            this.clearInput();
        });

        // 翻译按钮
        document.getElementById('translate-btn').addEventListener('click', () => {
            this.translate();
        });

        // 复制按钮
        document.getElementById('copy-btn').addEventListener('click', () => {
            this.copyResult();
        });

        // 输入框事件
        const inputText = document.getElementById('input-text');
        inputText.addEventListener('input', () => {
            this.onInputChange();
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });

        // 语言选择器按钮事件
        document.getElementById('source-lang').addEventListener('click', () => {
            this.toggleLanguageMenu('source');
        });

        document.getElementById('target-lang').addEventListener('click', () => {
            this.toggleLanguageMenu('target');
        });

        // 语言交换按钮
        document.getElementById('swap-languages').addEventListener('click', () => {
            this.swapLanguages();
        });

        // 绑定模型切换按钮
        this.bindModelButtons();

        // 语言选项点击事件
        this.bindLanguageOptionEvents();

        // 监听主题变化
        if (window.matchMedia) {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeQuery.addEventListener('change', () => {
                setTimeout(() => {
                    // 重新初始化主题，确保边框颜色正确
                    this.initTheme();
                    this.applyOptimalTransparency();
                }, 100);
            });
        }
    }

    setupWindow() {
        // 获取当前窗口引用（如果在 uTools 环境中）
        if (typeof utools !== 'undefined') {
            this.currentWindow = utools.getCurrentWindow?.();
        }

        // 设置窗口初始状态
        this.ensureRoundedCorners();
    }

    // 启动圆角监视器 - 最小化版本
    startRoundedCornersWatcher() {

        this.roundedCornersInterval = setInterval(() => {
            this.ensureRoundedCorners();
        }, 30000);


        window.addEventListener('resize', () => {
            setTimeout(() => {
                this.ensureRoundedCorners();
            }, 300);
        });
    }

    closeWindow() {
        try {
            // 清理监视器
            if (this.roundedCornersInterval) {
                clearInterval(this.roundedCornersInterval);
            }

            if (typeof utools !== 'undefined' && this.currentWindow) {
                this.currentWindow.close();
            } else if (window.close) {
                window.close();
            }
        } catch (error) {
            console.error('关闭窗口失败:', error);
        }
    }

    clearInput() {
        const inputText = document.getElementById('input-text');
        const outputText = document.getElementById('output-text');
        
        inputText.value = '';
        outputText.innerHTML = '<div class="placeholder-text">翻译结果将显示在这里...</div>';
        
        this.hideCopyButton();
        this.updateStatus('已清空');
        
        // 聚焦到输入框
        inputText.focus();
    }

    async translate() {
        const inputText = document.getElementById('input-text');
        const text = inputText.value.trim();

        if (!text) {
            this.updateStatus('请输入要翻译的文本');
            return;
        }

        if (this.isTranslating) {
            return;
        }

        // 检查是否有翻译模型
        const currentModel = this.getCurrentTranslateModel();
        if (!currentModel) {
            this.showError('请先配置翻译模型');
            return;
        }

        // 获取当前选择的语言
        const languages = this.getCurrentLanguages();
        if (!languages.targetLanguage) {
            this.showError('请选择目标语言');
            return;
        }

        this.isTranslating = true;
        this.showLoading();
        this.updateStatus('翻译中...');

        try {
            // 执行翻译
            const result = await this.executeTranslation(text, currentModel, languages);

            if (result.success) {
                this.displayTranslationResult(result);
                this.showCopyButton();
                this.updateStatus('翻译完成');
            } else {
                this.showError(result.error || '翻译失败');
            }
        } catch (error) {
            console.error('翻译失败:', error);
            this.showError('翻译失败，请稍后重试');
        } finally {
            this.isTranslating = false;
            this.hideLoading();
        }
    }



    copyResult() {
        const outputText = document.getElementById('output-text');
        const text = outputText.textContent;

        if (!text || text.includes('翻译结果将显示在这里')) {
            return;
        }

        try {
            // 尝试使用 uTools API 复制
            if (typeof utools !== 'undefined' && utools.copyText) {
                utools.copyText(text);
                this.updateStatus('已复制到剪贴板');
            } else {
                // 备用复制方法
                navigator.clipboard.writeText(text).then(() => {
                    this.updateStatus('已复制到剪贴板');
                }).catch(() => {
                    this.updateStatus('复制失败');
                });
            }
        } catch (error) {
            console.error('复制失败:', error);
            this.updateStatus('复制失败');
        }
    }

    onInputChange() {
        const inputText = document.getElementById('input-text');
        const hasText = inputText.value.trim().length > 0;
        
        if (hasText) {
            this.updateStatus('准备翻译');
        } else {
            this.updateStatus('就绪');
        }
    }

    handleKeydown(e) {
        // Ctrl/Cmd + Enter 翻译
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            this.translate();
        }
        
        // Escape 关闭窗口
        if (e.key === 'Escape') {
            e.preventDefault();
            this.closeWindow();
        }
        
        // Ctrl/Cmd + L 清空
        if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
            e.preventDefault();
            this.clearInput();
        }
    }

    // 绑定语言选项点击事件
    bindLanguageOptionEvents() {
        // 源语言选项
        const sourceOptions = document.querySelectorAll('#source-lang-menu .language-option');
        sourceOptions.forEach(option => {
            option.addEventListener('click', () => {
                this.selectLanguage('source', option.dataset.value, option);
            });
        });

        // 目标语言选项
        const targetOptions = document.querySelectorAll('#target-lang-menu .language-option');
        targetOptions.forEach(option => {
            option.addEventListener('click', () => {
                this.selectLanguage('target', option.dataset.value, option);
            });
        });

        // 点击其他地方关闭菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.language-selector')) {
                this.closeAllLanguageMenus();
            }
        });
    }

    // 切换语言菜单显示状态
    toggleLanguageMenu(type) {
        const menu = document.getElementById(`${type}-lang-menu`);
        const otherMenu = document.getElementById(`${type === 'source' ? 'target' : 'source'}-lang-menu`);

        if (!menu) return;

        // 关闭其他菜单
        if (otherMenu) {
            otherMenu.classList.remove('show');
        }

        // 切换当前菜单
        menu.classList.toggle('show');
    }

    // 关闭所有语言菜单
    closeAllLanguageMenus() {
        const sourceMenu = document.getElementById('source-lang-menu');
        const targetMenu = document.getElementById('target-lang-menu');

        if (sourceMenu) sourceMenu.classList.remove('show');
        if (targetMenu) targetMenu.classList.remove('show');
    }

    // 选择语言
    selectLanguage(type, _langCode, optionElement) {
        const button = document.getElementById(`${type}-lang`);
        const menu = document.getElementById(`${type}-lang-menu`);

        if (!button || !menu) return;

        // 更新按钮显示
        const languageText = button.querySelector('.language-text');
        if (languageText && optionElement) {
            const label = optionElement.querySelector('span').textContent;
            languageText.textContent = label;
        }

        // 更新选中状态
        menu.querySelectorAll('.language-option').forEach(opt => {
            opt.classList.remove('selected');
        });
        optionElement.classList.add('selected');

        // 关闭菜单
        menu.classList.remove('show');

        // 更新状态
        this.updateStatus(`已选择${type === 'source' ? '源' : '目标'}语言: ${optionElement.querySelector('span').textContent}`);
    }

    // 交换语言
    swapLanguages() {
        const sourceBtn = document.getElementById('source-lang');
        const targetBtn = document.getElementById('target-lang');
        const sourceMenu = document.getElementById('source-lang-menu');
        const targetMenu = document.getElementById('target-lang-menu');

        if (!sourceBtn || !targetBtn || !sourceMenu || !targetMenu) return;

        // 获取当前选中的语言
        const sourceSelected = sourceMenu.querySelector('.language-option.selected');
        const targetSelected = targetMenu.querySelector('.language-option.selected');

        if (!sourceSelected || !targetSelected) return;

        // 如果源语言是自动检测，不允许交换
        if (sourceSelected.dataset.value === 'auto') {
            this.updateStatus('自动检测语言无法交换');
            return;
        }

        // 交换按钮显示
        const sourceText = sourceBtn.querySelector('.language-text').textContent;
        const targetText = targetBtn.querySelector('.language-text').textContent;

        sourceBtn.querySelector('.language-text').textContent = targetText;
        targetBtn.querySelector('.language-text').textContent = sourceText;

        // 交换选中状态
        const sourceValue = sourceSelected.dataset.value;
        const targetValue = targetSelected.dataset.value;

        // 更新源语言菜单选中状态
        sourceMenu.querySelectorAll('.language-option').forEach(opt => {
            opt.classList.remove('selected');
            if (opt.dataset.value === targetValue) {
                opt.classList.add('selected');
            }
        });

        // 更新目标语言菜单选中状态
        targetMenu.querySelectorAll('.language-option').forEach(opt => {
            opt.classList.remove('selected');
            if (opt.dataset.value === sourceValue) {
                opt.classList.add('selected');
            }
        });

        this.updateStatus('语言已交换');
    }

    onLanguageChange() {
        // 保留原有方法以兼容性
        this.updateStatus('语言设置已更改');
    }

    // 应用最佳透明效果
    applyOptimalTransparency() {
        const container = document.querySelector('.capsule-container');
        if (container) {
            // 根据当前主题应用透明效果
            const currentTheme = document.documentElement.getAttribute('data-theme');

            if (currentTheme === 'dark') {
                container.style.background = 'rgba(48, 49, 51, 0.96)';
            } else {
                container.style.background = 'rgba(244, 244, 244, 0.96)';
            }

            container.style.backdropFilter = 'blur(8px)';
            container.style.webkitBackdropFilter = 'blur(8px)';
        }
    }



    // 确保圆角效果始终存在 - 轻量级检查
    ensureRoundedCorners() {
        const container = document.querySelector('.capsule-container');

        if (container) {
            // 只检查和修复关键的圆角样式，避免过度干预
            const currentRadius = getComputedStyle(container).borderRadius;
            if (!currentRadius || currentRadius === '0px' || currentRadius === 'none') {
                container.style.borderRadius = '12px';
                container.style.clipPath = 'inset(0 round 12px)';
                container.style.webkitClipPath = 'inset(0 round 12px)';
                container.style.overflow = 'hidden';
            }
        }
    }

    showLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        loadingOverlay.style.display = 'flex';
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        loadingOverlay.style.display = 'none';
    }

    showCopyButton() {
        const copyBtn = document.getElementById('copy-btn');
        copyBtn.style.display = 'flex';
    }

    hideCopyButton() {
        const copyBtn = document.getElementById('copy-btn');
        copyBtn.style.display = 'none';
    }

    showError(message) {
        const outputText = document.getElementById('output-text');

        // 根据错误类型提供更友好的提示
        let friendlyMessage = message;
        let suggestion = '';

        if (message.includes('翻译API未配置')) {
            suggestion = '<br><small style="color: #666;">请在主应用中配置相关API密钥</small>';
        } else if (message.includes('无法访问主应用')) {
            suggestion = '<br><small style="color: #666;">请确保从主应用启动胶囊翻译</small>';
        } else if (message.includes('请先配置翻译模型')) {
            suggestion = '<br><small style="color: #666;">请在主应用中添加翻译模型</small>';
        }

        outputText.innerHTML = `
            <div style="color: #ff3b30; padding: 10px; border-left: 3px solid #ff3b30; background: rgba(255, 59, 48, 0.1); border-radius: 4px;">
                <div style="font-weight: 500;">${friendlyMessage}</div>
                ${suggestion}
            </div>
        `;

        this.updateStatus('错误');
        this.hideCopyButton();
    }

    // 显示弹窗通知
    showNotification(message, type = 'info') {
        const container = document.getElementById('notification-container');
        if (!container) return;

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        container.appendChild(notification);

        // 触发显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // 自动移除
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // 更新状态显示（改为弹窗形式）
    updateStatus(message) {
        // 将状态更新改为弹窗通知
        if (message && message !== '就绪') {
            this.showNotification(message, 'info');
        }
    }

    // 初始化翻译模型选择器
    initTranslateModelSelector() {
        try {
            // 获取可用的翻译模型
            const availableModels = this.getAvailableModels();

            // 渲染模型选择器
            this.renderTranslateModelSelector(availableModels);

            // 设置当前选中的模型
            this.setCurrentSelectedModel();

            // 绑定模型按钮事件
            this.bindModelButtons();
        } catch (error) {
            console.error('初始化翻译模型选择器失败:', error);
        }
    }

    // 渲染翻译模型选择器
    renderTranslateModelSelector(models) {
        const selectorContainer = document.getElementById('translate-model-selector');
        if (!selectorContainer || !models || models.length === 0) {
            return;
        }

        // 清空现有内容
        selectorContainer.innerHTML = '';

        // 显示前4个模型
        const modelsToShow = models.slice(0, 4);

        modelsToShow.forEach(model => {
            const button = document.createElement('div');
            button.className = 'translate-model-btn';
            button.dataset.service = model.service;
            button.dataset.model = model.model || 'traditional';
            button.dataset.type = model.type || 'traditional';
            button.title = this.getModelDisplayName(model);

            // 获取服务图标
            const iconHtml = this.getServiceIconHtml(model.service);

            button.innerHTML = `
                ${iconHtml}
                <span class="model-name">${this.getServiceShortName(model.service)}</span>
            `;

            selectorContainer.appendChild(button);
        });
    }

    // 设置当前选中的模型
    setCurrentSelectedModel() {
        const currentModel = this.getCurrentTranslateModel();
        if (currentModel) {
            const modelBtn = document.querySelector(`[data-service="${currentModel.service}"]`);
            if (modelBtn) {
                modelBtn.classList.add('active');
            }
        } else {
            // 如果没有当前模型，选择第一个
            const firstBtn = document.querySelector('.translate-model-btn');
            if (firstBtn) {
                firstBtn.classList.add('active');
                this.saveCurrentModel(firstBtn.dataset.service);
            }
        }
    }

    // 绑定模型按钮事件
    bindModelButtons() {
        const modelButtons = document.querySelectorAll('.translate-model-btn');
        modelButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.switchModel(button.dataset.service);
            });
        });
    }

    // 获取服务图标HTML
    getServiceIconHtml(service) {
        const iconPath = `../assets/icons/${service}-icon.svg`;
        return `<img src="${iconPath}" alt="${service}" class="model-icon" onerror="this.style.display='none'">`;
    }

    // 获取服务短名称
    getServiceShortName(service) {
        const serviceNames = {
            'google': 'Google',
            'baidu': '百度',
            'openai': 'OpenAI',
            'anthropic': 'Claude',
            'tencent': '腾讯',
            'aliyun': '阿里'
        };
        return serviceNames[service] || service;
    }

    // 获取模型显示名称
    getModelDisplayName(model) {
        const serviceName = this.getServiceShortName(model.service);
        if (model.type === 'traditional') {
            return `${serviceName} 翻译`;
        } else {
            return `${serviceName} - ${model.model}`;
        }
    }

    // 切换翻译模型
    switchModel(service) {
        try {
            // 更新按钮状态
            document.querySelectorAll('.translate-model-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            const selectedBtn = document.querySelector(`[data-service="${service}"]`);
            if (!selectedBtn) {
                console.error('未找到对应的模型按钮:', service);
                return;
            }

            selectedBtn.classList.add('active');
            const serviceName = selectedBtn.title;

            // 构建模型对象
            const modelData = {
                service: service,
                model: selectedBtn.dataset.model || 'traditional',
                type: selectedBtn.dataset.type || 'traditional'
            };

            // 保存当前选择的模型到本地存储
            this.saveCurrentModel(service, modelData);

            // 如果可以访问主应用，同步到主应用
            if (this.canAccessMainApp()) {
                try {
                    window.parent.ocrPlugin.configManager.setCurrentTranslateModel(
                        modelData.service,
                        modelData.model,
                        modelData.type
                    );
                    console.log('已同步模型选择到主应用');
                } catch (error) {
                    console.warn('同步模型到主应用失败:', error);
                }
            }

            this.showNotification(`已切换到 ${serviceName}`, 'success');
        } catch (error) {
            console.error('切换模型失败:', error);
            this.showNotification('切换模型失败', 'error');
        }
    }

    // 获取当前翻译模型
    getCurrentTranslateModel() {
        try {
            // 首先尝试从主应用获取
            if (this.canAccessMainApp()) {
                const currentModel = window.parent.ocrPlugin.configManager.getCurrentTranslateModel();
                if (currentModel) {
                    // 缓存到本地存储
                    localStorage.setItem('capsule-current-model', JSON.stringify(currentModel));
                    return currentModel;
                }
            }

            // 如果无法从主应用获取，从本地存储获取
            const savedModel = localStorage.getItem('capsule-current-model');
            if (savedModel) {
                try {
                    return JSON.parse(savedModel);
                } catch (e) {
                    console.warn('解析缓存模型失败:', e);
                }
            }

            // 默认返回第一个可用模型
            const models = this.getAvailableModels();
            if (models.length > 0) {
                const defaultModel = models[0];
                // 缓存默认模型
                localStorage.setItem('capsule-current-model', JSON.stringify(defaultModel));
                return defaultModel;
            }

            return null;
        } catch (error) {
            console.error('获取当前翻译模型失败:', error);
            return null;
        }
    }

    // 保存当前模型
    saveCurrentModel(service, modelData = null) {
        try {
            let selectedModel = modelData;

            if (!selectedModel) {
                const models = this.getAvailableModels();
                selectedModel = models.find(m => m.service === service);
            }

            if (selectedModel) {
                localStorage.setItem('capsule-current-model', JSON.stringify(selectedModel));
                console.log('已保存当前模型:', selectedModel);
            } else {
                console.warn('未找到要保存的模型:', service);
            }
        } catch (error) {
            console.error('保存当前模型失败:', error);
        }
    }

    // 获取可用的翻译模型
    getAvailableModels() {
        try {
            // 首先尝试从主应用获取
            if (this.canAccessMainApp()) {
                const models = window.parent.ocrPlugin.configManager.getTranslateModels();
                if (models && models.length > 0) {
                    return models;
                }
            }

            // 尝试从本地存储获取缓存的模型列表
            const cachedModels = localStorage.getItem('capsule-available-models');
            if (cachedModels) {
                try {
                    const parsed = JSON.parse(cachedModels);
                    if (Array.isArray(parsed) && parsed.length > 0) {
                        return parsed;
                    }
                } catch (e) {
                    console.warn('解析缓存模型列表失败:', e);
                }
            }

            // 如果无法从主应用获取，返回默认模型列表
            return [
                { service: 'google', model: 'traditional', type: 'traditional' },
                { service: 'baidu', model: 'traditional', type: 'traditional' },
                { service: 'openai', model: 'gpt-3.5-turbo', type: 'ai' },
                { service: 'anthropic', model: 'claude-3-haiku', type: 'ai' }
            ];
        } catch (error) {
            console.error('获取可用模型失败:', error);
            return [];
        }
    }

    // 检查是否可以访问主应用
    canAccessMainApp() {
        try {
            return typeof utools !== 'undefined' &&
                   window.parent &&
                   window.parent !== window &&
                   window.parent.ocrPlugin &&
                   window.parent.ocrPlugin.configManager;
        } catch (error) {
            return false;
        }
    }

    // 同步主应用的配置到小窗口
    syncConfigFromMainApp() {
        try {
            if (this.canAccessMainApp()) {
                const mainApp = window.parent.ocrPlugin;

                // 缓存翻译模型列表
                const models = mainApp.configManager.getTranslateModels();
                if (models && models.length > 0) {
                    localStorage.setItem('capsule-available-models', JSON.stringify(models));
                }

                // 缓存当前翻译模型
                const currentModel = mainApp.configManager.getCurrentTranslateModel();
                if (currentModel) {
                    localStorage.setItem('capsule-current-model', JSON.stringify(currentModel));
                }

                console.log('已同步主应用配置到小窗口');
                return true;
            }
            return false;
        } catch (error) {
            console.error('同步主应用配置失败:', error);
            return false;
        }
    }

    // 获取当前语言设置
    getCurrentLanguages() {
        const sourceBtn = document.getElementById('source-lang');
        const targetBtn = document.getElementById('target-lang');
        const sourceMenu = document.getElementById('source-lang-menu');
        const targetMenu = document.getElementById('target-lang-menu');

        if (!sourceBtn || !targetBtn || !sourceMenu || !targetMenu) {
            return { sourceLanguage: null, targetLanguage: null };
        }

        const sourceSelected = sourceMenu.querySelector('.language-option.selected');
        const targetSelected = targetMenu.querySelector('.language-option.selected');

        const sourceLanguage = sourceSelected ? {
            langCode: sourceSelected.dataset.value,
            value: sourceSelected.textContent.trim()
        } : null;

        const targetLanguage = targetSelected ? {
            langCode: targetSelected.dataset.value,
            value: targetSelected.textContent.trim()
        } : null;

        return { sourceLanguage, targetLanguage };
    }

    // 执行翻译
    async executeTranslation(text, model, languages) {
        try {
            // 如果可以访问主应用的翻译服务，使用主应用的服务
            if (typeof utools !== 'undefined' && window.parent && window.parent.ocrPlugin) {
                return await this.executeTranslationViaMainApp(text, model, languages);
            }

            // 否则使用内置的翻译逻辑
            return await this.executeTranslationBuiltIn(text, model, languages);
        } catch (error) {
            console.error('执行翻译失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 通过主应用执行翻译
    async executeTranslationViaMainApp(text, model, languages) {
        try {
            const ocrPlugin = window.parent.ocrPlugin;
            if (!ocrPlugin || !ocrPlugin.configManager || !ocrPlugin.ocrServices) {
                throw new Error('无法访问主应用的翻译服务');
            }

            const config = ocrPlugin.configManager.getConfig();
            if (!config) {
                throw new Error('无法获取主应用配置');
            }

            let result;

            if (model.type === 'traditional') {
                // 传统翻译API
                const translateConfig = ocrPlugin.configManager.getTraditionalTranslateConfig(model.service);
                if (!translateConfig) {
                    throw new Error(`${model.service}翻译API未配置，请在主应用中配置相关API密钥`);
                }

                // 验证语言代码
                if (!languages.sourceLanguage || !languages.targetLanguage) {
                    throw new Error('源语言或目标语言未设置');
                }

                const sourceCode = this.convertLanguageCodeForTraditional(languages.sourceLanguage.langCode, model.service);
                const targetCode = this.convertLanguageCodeForTraditional(languages.targetLanguage.langCode, model.service);

                result = await ocrPlugin.ocrServices.performTraditionalTranslation(
                    text,
                    sourceCode,
                    targetCode,
                    model.service,
                    translateConfig
                );
            } else {
                // AI模型翻译
                if (!languages.targetLanguage) {
                    throw new Error('目标语言未设置');
                }

                result = await ocrPlugin.ocrServices.performTranslation(
                    text,
                    model.service,
                    model.model,
                    config,
                    null, // 不使用流式输出
                    languages.targetLanguage,
                    null
                );
            }

            // 验证翻译结果
            if (!result) {
                throw new Error('翻译服务返回空结果');
            }

            if (!result.success) {
                throw new Error(result.error || '翻译失败');
            }

            return result;
        } catch (error) {
            console.error('通过主应用翻译失败:', error);
            throw error;
        }
    }

    // 内置翻译逻辑（简化版）
    async executeTranslationBuiltIn(text, model, languages) {
        // 这里可以实现简化的翻译逻辑，比如调用免费的翻译API
        // 目前返回一个模拟结果，使用传入的参数
        const sourceLanguage = languages.sourceLanguage?.value || '自动检测';
        const targetLanguage = languages.targetLanguage?.value || '中文';
        const modelName = this.getModelDisplayName(model);

        return new Promise((resolve) => {
            setTimeout(() => {
                resolve({
                    success: true,
                    translatedText: `[${modelName}模拟翻译]\n源语言: ${sourceLanguage}\n目标语言: ${targetLanguage}\n原文: ${text}\n译文: ${text}`,
                    text: `[${modelName}模拟翻译] ${text}`
                });
            }, 1000);
        });
    }

    // 转换语言代码为传统API格式
    convertLanguageCodeForTraditional(langCode, service) {
        const languageMaps = {
            baidu: {
                'auto': 'auto',
                'zh': 'zh',
                'zh-cn': 'zh',
                'en': 'en',
                'ja': 'jp',
                'ko': 'kor'
            },
            google: {
                'auto': 'auto',
                'zh': 'zh-cn',
                'zh-cn': 'zh-cn',
                'en': 'en',
                'ja': 'ja',
                'ko': 'ko'
            }
        };

        const map = languageMaps[service];
        return map ? (map[langCode] || langCode) : langCode;
    }

    // 显示翻译结果
    displayTranslationResult(result) {
        const outputText = document.getElementById('output-text');
        const translatedText = result.translatedText || result.text || result.fullText || '';

        if (translatedText) {
            outputText.innerHTML = `<div class="translation-result">${translatedText}</div>`;
        } else {
            outputText.innerHTML = '<div class="placeholder-text">翻译结果为空</div>';
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.capsuleTranslator = new CapsuleTranslator();
});

// 如果在 uTools 环境中，监听父窗口消息
if (typeof utools !== 'undefined') {
    // 可以在这里添加与主窗口的通信逻辑
}

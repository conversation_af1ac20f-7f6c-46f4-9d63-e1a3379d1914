<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胶囊翻译测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #1565c0;
        }
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #e3f2fd;
            border-left: 4px solid #1976d2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>胶囊翻译功能测试</h1>
        
        <div class="test-section">
            <h3>功能测试</h3>
            <button onclick="openCapsuleWindow()">打开胶囊翻译窗口</button>
            <button onclick="openWithText()">带文本打开</button>
            <button onclick="testModelSync()">测试模型同步</button>
            <div class="status" id="status">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <h3>胶囊翻译窗口预览</h3>
            <div class="iframe-container">
                <iframe src="capsule-window.html" id="capsule-iframe"></iframe>
            </div>
        </div>

        <div class="test-section">
            <h3>测试说明</h3>
            <ul>
                <li><strong>打开胶囊翻译窗口</strong>：测试基本的窗口打开功能</li>
                <li><strong>带文本打开</strong>：测试传递文本到小窗口的功能</li>
                <li><strong>测试模型同步</strong>：测试模型选择器的显示和切换功能</li>
                <li><strong>窗口预览</strong>：在iframe中预览胶囊翻译界面</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟uTools环境
        window.utools = {
            getPayload: () => null,
            isDarkColors: () => false,
            getCurrentWindow: () => ({
                close: () => console.log('窗口关闭'),
                center: () => console.log('窗口居中'),
                focus: () => console.log('窗口聚焦')
            }),
            copyText: (text) => {
                navigator.clipboard.writeText(text).then(() => {
                    updateStatus('文本已复制到剪贴板: ' + text);
                });
            }
        };

        // 模拟主应用的ocrPlugin
        window.ocrPlugin = {
            configManager: {
                getTranslateModels: () => [
                    { service: 'google', model: 'traditional', type: 'traditional' },
                    { service: 'baidu', model: 'traditional', type: 'traditional' },
                    { service: 'openai', model: 'gpt-3.5-turbo', type: 'ai' },
                    { service: 'anthropic', model: 'claude-3-haiku', type: 'ai' }
                ],
                getCurrentTranslateModel: () => ({
                    service: 'google',
                    model: 'traditional',
                    type: 'traditional'
                }),
                getConfig: () => ({
                    google: { apiKey: 'test' },
                    baidu: { apiKey: 'test', secretKey: 'test' }
                }),
                getTraditionalTranslateConfig: (service) => ({
                    apiKey: 'test-key',
                    secretKey: 'test-secret'
                })
            },
            ocrServices: {
                performTraditionalTranslation: async (text, source, target, service, config) => {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    return {
                        success: true,
                        translatedText: `[${service}翻译] ${text} -> 翻译结果`
                    };
                },
                performTranslation: async (text, service, model, config, onStream, targetLang) => {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    return {
                        success: true,
                        text: `[${service}-${model}] ${text} -> AI翻译结果`
                    };
                }
            }
        };

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function openCapsuleWindow() {
            updateStatus('正在打开胶囊翻译窗口...');
            window.open('capsule-window.html', 'capsule', 'width=380,height=520,resizable=yes');
        }

        function openWithText() {
            updateStatus('正在打开带文本的胶囊翻译窗口...');
            const testText = 'Hello, this is a test text for translation.';
            window.pendingTranslateText = testText;
            window.open('capsule-window.html?text=' + encodeURIComponent(testText), 'capsule', 'width=380,height=520,resizable=yes');
        }

        function testModelSync() {
            updateStatus('测试模型同步功能...');
            const iframe = document.getElementById('capsule-iframe');
            if (iframe.contentWindow && iframe.contentWindow.capsuleTranslator) {
                iframe.contentWindow.capsuleTranslator.syncConfigFromMainApp();
                iframe.contentWindow.capsuleTranslator.initTranslateModelSelector();
                updateStatus('模型同步测试完成，请查看iframe中的模型选择器');
            } else {
                updateStatus('iframe未加载完成，请稍后再试');
            }
        }

        // 监听iframe加载完成
        document.getElementById('capsule-iframe').onload = function() {
            updateStatus('胶囊翻译界面已加载完成');
        };
    </script>
</body>
</html>

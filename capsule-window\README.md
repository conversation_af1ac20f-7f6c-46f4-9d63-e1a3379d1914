# 胶囊翻译小窗功能实现

## 功能概述

胶囊翻译小窗是OCR Pro插件的一个独立翻译界面，提供轻量级的翻译功能，支持与主应用的数据同步和独立运行。

## 主要功能

### 1. 模型显示和选择功能 ✅

- **动态模型加载**：从主应用获取可用的翻译模型列表
- **模型切换**：支持在不同翻译服务之间切换（Google、百度、OpenAI、Claude等）
- **状态同步**：模型选择状态与主应用保持同步
- **本地缓存**：支持离线模式下的模型信息缓存

**实现细节**：
- `initTranslateModelSelector()`: 初始化模型选择器
- `renderTranslateModelSelector()`: 动态渲染模型按钮
- `switchModel()`: 处理模型切换逻辑
- `syncConfigFromMainApp()`: 同步主应用配置

### 2. 数据传递机制 ✅

- **主应用通信**：通过`window.parent`访问主应用的配置和服务
- **配置同步**：自动同步翻译模型、API配置等信息
- **文本传递**：支持从uTools payload、URL参数、全局变量获取待翻译文本
- **状态缓存**：使用localStorage缓存配置信息

**实现细节**：
- `canAccessMainApp()`: 检查是否可以访问主应用
- `handleIncomingText()`: 处理传入的文本
- `getCurrentTranslateModel()`: 获取当前翻译模型
- `getAvailableModels()`: 获取可用模型列表

### 3. 翻译实现逻辑 ✅

- **双模式翻译**：支持传统翻译API和AI模型翻译
- **主应用集成**：优先使用主应用的翻译服务
- **独立运行**：支持脱离主应用的简化翻译功能
- **错误处理**：完善的错误提示和降级处理

**实现细节**：
- `executeTranslation()`: 翻译执行入口
- `executeTranslationViaMainApp()`: 通过主应用执行翻译
- `executeTranslationBuiltIn()`: 内置翻译逻辑
- `convertLanguageCodeForTraditional()`: 语言代码转换

## 技术特性

### 界面设计
- **响应式布局**：适配不同窗口尺寸
- **主题适配**：支持亮色/暗色主题自动切换
- **圆角窗口**：自定义圆角效果，避免系统限制
- **透明背景**：支持背景透明和毛玻璃效果

### 用户体验
- **快捷键支持**：Ctrl+Enter翻译、Esc关闭、Ctrl+L清空
- **智能提示**：友好的错误提示和操作建议
- **状态反馈**：实时的操作状态通知
- **一键复制**：翻译结果一键复制到剪贴板

### 数据管理
- **配置缓存**：本地缓存翻译模型和配置信息
- **状态持久化**：保存用户的模型选择偏好
- **数据验证**：完善的输入验证和错误处理
- **降级处理**：主应用不可用时的备用方案

## 文件结构

```
capsule-window/
├── capsule-window.html    # 主界面HTML
├── capsule-window.css     # 样式文件
├── capsule-window.js      # 核心逻辑
├── test.html             # 功能测试页面
└── README.md             # 说明文档
```

## 使用方式

### 从主应用启动
```javascript
// 在主应用中调用
this.handleCapsuleTranslateFeature();
```

### 独立测试
```bash
# 打开测试页面
file:///path/to/capsule-window/test.html
```

## 配置要求

### 主应用依赖
- `window.parent.ocrPlugin.configManager`: 配置管理器
- `window.parent.ocrPlugin.ocrServices`: 翻译服务
- 已配置的翻译模型和API密钥

### 运行环境
- uTools环境（生产环境）
- 现代浏览器（测试环境）
- 支持ES6+语法

## 测试功能

测试页面提供以下测试功能：
1. **基本窗口打开**：测试小窗口的基本显示
2. **文本传递**：测试文本参数的传递和显示
3. **模型同步**：测试模型选择器的同步功能
4. **翻译功能**：测试完整的翻译流程

## 已知限制

1. **依赖主应用**：完整功能需要主应用支持
2. **API配置**：需要在主应用中配置相关API密钥
3. **网络依赖**：翻译功能需要网络连接
4. **浏览器兼容**：需要支持ES6+的现代浏览器

## 后续优化

1. **离线翻译**：集成本地翻译引擎
2. **批量翻译**：支持多段文本批量处理
3. **历史记录**：翻译历史的本地存储
4. **快捷操作**：更多的键盘快捷键支持

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>胶囊翻译</title>
    <link rel="stylesheet" href="capsule-window.css">
</head>
<body>
    <div id="capsule-container" class="capsule-container">
        <!-- 顶部拖拽区域 -->
        <div class="drag-area" id="drag-area">
            <div class="window-controls">
                <button class="close-btn" id="close-btn" title="关闭">
                    <svg width="12" height="12" viewBox="0 0 12 12">
                        <path d="M1 1l10 10M11 1L1 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-area">
            <!-- 输入区域 -->
            <div class="input-section">
                <div class="input-container">
                    <textarea 
                        id="input-text" 
                        class="input-text" 
                        placeholder="输入要翻译的文本..."
                        rows="3"
                    ></textarea>
                    <div class="input-actions">
                        <button class="action-btn clear-btn" id="clear-btn" title="清空">
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="m14.622 17.897-10.68-2.913"/>
                                <path d="M18.376 2.622a1 1 0 1 1 3.002 3.002L17.36 9.643a.5.5 0 0 0 0 .707l.944.944a2.41 2.41 0 0 1 0 3.408l-.944.944a.5.5 0 0 1-.707 0L8.354 7.348a.5.5 0 0 1 0-.707l.944-.944a2.41 2.41 0 0 1 3.408 0l.944.944a.5.5 0 0 0 .707 0z"/>
                                <path d="M9 8c-1.804 2.71-3.97 3.46-6.583 3.948a .507.507 0 0 0-.302.819l7.32 8.883a1 1 0 0 0 1.185.204C12.735 20.405 16 16.792 16 15"/>
                            </svg>
                        </button>
                        <button class="action-btn translate-btn" id="translate-btn" title="翻译">
                            <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="m5 8 6 6"/>
                                <path d="m4 14 6-6 2-3"/>
                                <path d="M2 5h12"/>
                                <path d="M7 2h1"/>
                                <path d="m22 22-5-10-5 10"/>
                                <path d="M14 18h6"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 中间操作区域 -->
            <div class="translate-controls">
                <div class="left-controls">
                    <!-- 翻译模型选择器 -->
                    <div id="translate-model-selector" class="translate-model-selector">
                        <!-- 模型按钮将由JavaScript动态生成 -->
                    </div>
                </div>
                <div class="center-controls">
                    <!-- 中心区域留空，状态提示改为弹窗形式 -->
                </div>
                <div class="right-controls">
                    <!-- 语言选择器 -->
                    <div class="language-selector-container">
                        <!-- 源语言选择器 -->
                        <div class="language-selector">
                            <button id="source-lang" class="language-select" title="源语言">
                                <span class="language-text">自动</span>
                            </button>
                            <div id="source-lang-menu" class="language-select-menu">
                                <div class="language-option selected" data-value="auto">
                                    <span>自动</span>
                                </div>
                                <div class="language-option" data-value="zh">
                                    <span>中文</span>
                                </div>
                                <div class="language-option" data-value="en">
                                    <span>英文</span>
                                </div>
                                <div class="language-option" data-value="ja">
                                    <span>日文</span>
                                </div>
                                <div class="language-option" data-value="ko">
                                    <span>韩文</span>
                                </div>
                            </div>
                        </div>

                        <!-- 语言交换按钮 -->
                        <button id="swap-languages" class="language-swap-btn" title="交换语言">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left-right">
                                <path d="M8 3 4 7l4 4"/>
                                <path d="M4 7h16"/>
                                <path d="m16 21 4-4-4-4"/>
                                <path d="M20 17H4"/>
                            </svg>
                        </button>

                        <!-- 目标语言选择器 -->
                        <div class="language-selector">
                            <button id="target-lang" class="language-select" title="目标语言">
                                <span class="language-text">中文</span>
                            </button>
                            <div id="target-lang-menu" class="language-select-menu">
                                <div class="language-option selected" data-value="zh">
                                    <span>中文</span>
                                </div>
                                <div class="language-option" data-value="en">
                                    <span>英文</span>
                                </div>
                                <div class="language-option" data-value="ja">
                                    <span>日文</span>
                                </div>
                                <div class="language-option" data-value="ko">
                                    <span>韩文</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 输出区域 -->
            <div class="output-section">
                <div class="output-container">
                    <div class="output-text" id="output-text">
                        <div class="placeholder-text">翻译结果将显示在这里...</div>
                    </div>
                    <div class="output-actions">
                        <button class="action-btn copy-btn" id="copy-btn" title="复制" style="display: none;">
                            <svg width="16" height="16" viewBox="0 0 16 16">
                                <path d="M4 2h6a2 2 0 012 2v6M4 6h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2z" 
                                      stroke="currentColor" stroke-width="1.2" fill="none" stroke-linecap="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>



        <!-- 加载指示器 -->
        <div class="loading-overlay" id="loading-overlay" style="display: none;">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <div class="loading-text">翻译中...</div>
            </div>
        </div>
    </div>

    <!-- 弹窗通知容器 -->
    <div id="notification-container" class="notification-container"></div>

    <script src="capsule-window.js"></script>
</body>
</html>
